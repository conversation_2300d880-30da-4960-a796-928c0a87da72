
#课程

https://www.yojiang.cn/pc/lesson/28

# 奶爸英语
https://bsdcpp.github.io/2020/10/26/%E6%81%B6%E9%AD%94%E5%A5%B6%E7%88%B8%E8%8B%B1%E8%AF%AD%E5%AD%A6%E4%B9%A0%E6%96%B9%E6%B3%95%EF%BC%88%E8%BD%AC%EF%BC%89/



https://www.youtube.com/watch?v=q9UsW-uKu1M

https://shopify.engineering/a-flexible-framework-for-effective-pair-programming

https://www.teamblind.com/post/Shopify-pair-programming-interview-iM6F08pH

https://leetcode.com/problems/design-underground-system/description/


design a robot that can turn and move. With follow ups like multiple robots, or the floor has obstacles