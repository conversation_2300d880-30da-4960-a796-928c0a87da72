#!/usr/bin/env python3
import re

def cleanup_dialogue(input_file, output_file):
    """
    Clean up the dialogue file by:
    1. Removing lines that only contain timestamps and "-->".
    2. Removing the timestamp and "-->" parts from lines that also contain dialogue.
    3. Removing all numbers from the text.
    """
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    cleaned_lines = []

    for line in lines:
        # Skip empty lines
        if not line.strip():
            continue

        # Remove timestamp and "-->" parts
        line = re.sub(r'[\d,.:]+\s*-->\s*[\d,.:\s]+', '', line)

        # Remove any remaining timestamps (like "00:04: 49,245")
        line = re.sub(r'\d+:\d+:\s*\d+,\d+', '', line)

        # Remove standalone numbers
        line = re.sub(r'\b\d+\b', '', line)

        # Remove any remaining numbers with punctuation
        line = re.sub(r'\d+[,.:]\d+', '', line)

        # Remove any remaining commas followed by spaces that might be from timestamps
        line = re.sub(r',\s+', ' ', line)

        # Restore punctuation spacing
        line = re.sub(r'\s+([.,!?;:])', r'\1', line)

        # Clean up extra spaces
        line = re.sub(r'\s+', ' ', line).strip()

        # Skip lines that are now empty after cleaning
        if not line:
            continue

        cleaned_lines.append(line + '\n')

    # Write cleaned lines to output file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.writelines(cleaned_lines)

    print(f"Cleaned up {len(lines)} lines to {len(cleaned_lines)} lines")
    print(f"Saved cleaned dialogue to {output_file}")

if __name__ == "__main__":
    input_file = "movie/sex/sex_education_dialogue.txt"
    output_file = "movie/sex/sex_education_dialogue_clean.txt"
    cleanup_dialogue(input_file, output_file)
