#!/usr/bin/env python3
import re

def split_vocabulary_file(input_file, key_vocab_file, supp_vocab_file):
    """
    Split the extracted vocabulary file into two separate files:
    - Key Vocabulary
    - Supplementary Vocabulary
    """
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Split content into sections
    sections = re.split(r'(=== .+? ===)', content)
    
    key_vocab_content = []
    supp_vocab_content = []
    
    current_section = None
    
    for i, section in enumerate(sections):
        if section.startswith('===') and section.endswith('==='):
            current_section = section
        elif current_section:
            # Check if this is a Key Vocabulary or Supplementary Vocabulary section
            if 'Key Vocabulary' in current_section:
                key_vocab_content.append(current_section)
                key_vocab_content.append(section)
            elif 'Supplementary Vocabulary' in current_section:
                supp_vocab_content.append(current_section)
                supp_vocab_content.append(section)
    
    # Write Key Vocabulary file
    with open(key_vocab_file, 'w', encoding='utf-8') as f:
        f.write(''.join(key_vocab_content))
    
    # Write Supplementary Vocabulary file
    with open(supp_vocab_file, 'w', encoding='utf-8') as f:
        f.write(''.join(supp_vocab_content))
    
    # Count sections
    key_sections = len([s for s in key_vocab_content if s.startswith('===')])
    supp_sections = len([s for s in supp_vocab_content if s.startswith('===')])
    
    print(f"Split completed!")
    print(f"Key Vocabulary sections: {key_sections}")
    print(f"Supplementary Vocabulary sections: {supp_sections}")
    print(f"Key Vocabulary saved to: {key_vocab_file}")
    print(f"Supplementary Vocabulary saved to: {supp_vocab_file}")

def main():
    input_file = "7.初中级/english_pod/extracted_vocabulary.txt"
    key_vocab_file = "7.初中级/english_pod/key_vocabulary.txt"
    supp_vocab_file = "7.初中级/english_pod/supplementary_vocabulary.txt"
    
    split_vocabulary_file(input_file, key_vocab_file, supp_vocab_file)

if __name__ == "__main__":
    main()
