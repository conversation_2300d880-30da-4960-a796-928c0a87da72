#!/usr/bin/env python3
import re
import csv

def parse_vocabulary_file(file_path):
    """Parse vocabulary file and extract structured data."""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Split content into sections
    sections = re.split(r'(=== .+? ===)', content)
    
    vocabulary_data = []
    current_source = ""
    
    for i, section in enumerate(sections):
        if section.startswith('===') and section.endswith('==='):
            # Extract source file and vocabulary type
            match = re.match(r'=== (.+?) - (Key Vocabulary|Supplementary Vocabulary) ===', section)
            if match:
                current_source = match.group(1)
                vocab_type = match.group(2)
        elif current_source and section.strip():
            # Parse vocabulary entries
            lines = section.strip().split('\n')
            current_entry = {}
            
            for line in lines:
                line = line.strip()
                if not line or line.startswith('Visit the Online'):
                    continue
                
                # Try to parse vocabulary entry
                # Format: word [part_of_speech] definition
                parts = line.split()
                if len(parts) >= 2:
                    word = parts[0]
                    
                    # Look for part of speech patterns
                    pos_match = re.search(r'\b(Noun|Verb|Adjective|Phrase|Adverb|Preposition|Conjunction)\b', line)
                    if pos_match:
                        part_of_speech = pos_match.group(1)
                        # Extract definition (everything after part of speech)
                        definition_start = line.find(part_of_speech) + len(part_of_speech)
                        definition = line[definition_start:].strip()
                    else:
                        part_of_speech = ""
                        definition = ' '.join(parts[1:])
                    
                    if word and definition:
                        vocabulary_data.append({
                            'Source': current_source,
                            'Type': vocab_type,
                            'Word': word,
                            'Part of Speech': part_of_speech,
                            'Definition': definition
                        })
    
    return vocabulary_data

def save_to_csv(data, output_file):
    """Save vocabulary data to CSV file."""
    if not data:
        print("No data to save")
        return
    
    fieldnames = ['Source', 'Type', 'Word', 'Part of Speech', 'Definition']
    
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(data)
    
    print(f"Saved {len(data)} entries to {output_file}")

def main():
    # Process Key Vocabulary
    print("Processing Key Vocabulary...")
    key_vocab_data = parse_vocabulary_file("7.初中级/english_pod/key_vocabulary.txt")
    save_to_csv(key_vocab_data, "7.初中级/english_pod/key_vocabulary.csv")
    
    # Process Supplementary Vocabulary
    print("Processing Supplementary Vocabulary...")
    supp_vocab_data = parse_vocabulary_file("7.初中级/english_pod/supplementary_vocabulary.txt")
    save_to_csv(supp_vocab_data, "7.初中级/english_pod/supplementary_vocabulary.csv")
    
    # Create combined CSV
    print("Creating combined vocabulary file...")
    all_vocab_data = key_vocab_data + supp_vocab_data
    save_to_csv(all_vocab_data, "7.初中级/english_pod/all_vocabulary.csv")
    
    print("CSV conversion completed!")

if __name__ == "__main__":
    main()
