#!/usr/bin/env python3
import re
import csv

def create_clean_csv_from_txt(input_file, output_file):
    """Create a clean CSV from the vocabulary text file."""
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Split by section headers
    sections = re.split(r'(=== .+? ===)', content)
    
    vocabulary_data = []
    current_source = ""
    vocab_type = ""
    
    for section in sections:
        if section.startswith('===') and section.endswith('==='):
            # Extract source and type
            match = re.match(r'=== (.+?) - (Key Vocabulary|Supplementary Vocabulary) ===', section)
            if match:
                current_source = match.group(1)
                vocab_type = match.group(2)
        elif current_source and section.strip():
            # Clean the section
            lines = section.strip().split('\n')
            clean_text = []
            
            for line in lines:
                line = line.strip()
                # Skip copyright and empty lines
                if (not line or 
                    line.startswith('Visit the Online') or 
                    'Praxis Language Ltd' in line or
                    line.startswith('c 20')):
                    continue
                clean_text.append(line)
            
            # Join all text and add as one entry for manual processing
            if clean_text:
                full_text = ' '.join(clean_text)
                vocabulary_data.append({
                    'Source': current_source,
                    'Type': vocab_type,
                    'Content': full_text
                })
    
    # Save to CSV
    with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['Source', 'Type', 'Content']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(vocabulary_data)
    
    print(f"Created {output_file} with {len(vocabulary_data)} sections")

def main():
    # Create clean CSVs
    create_clean_csv_from_txt("7.初中级/english_pod/key_vocabulary.txt", 
                             "7.初中级/english_pod/key_vocabulary_clean.csv")
    
    create_clean_csv_from_txt("7.初中级/english_pod/supplementary_vocabulary.txt", 
                             "7.初中级/english_pod/supplementary_vocabulary_clean.csv")

if __name__ == "__main__":
    main()
