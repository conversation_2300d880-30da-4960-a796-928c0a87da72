#!/usr/bin/env python3
import os
import re
import glob
import PyPDF2
from tqdm import tqdm

def extract_text_from_pdf(pdf_path):
    """Extract text from a PDF file."""
    try:
        with open(pdf_path, 'rb') as pdf_file:
            pdf_reader = PyPDF2.PdfReader(pdf_file)
            text = ""
            
            # Extract text from each page
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text += page.extract_text() + "\n\n"
            
            return text
    except Exception as e:
        print(f"Error extracting text from {pdf_path}: {e}")
        return ""

def extract_vocabulary_sections(text, filename):
    """Extract Key Vocabulary and Supplementary Vocabulary sections from text."""
    vocabulary_entries = []
    
    # Patterns to find vocabulary sections
    key_vocab_pattern = r'Key Vocabulary(.*?)(?=Supplementary Vocabulary|$)'
    supp_vocab_pattern = r'Supplementary Vocabulary(.*?)(?=\n\n[A-Z]|$)'
    
    # Find Key Vocabulary section
    key_vocab_match = re.search(key_vocab_pattern, text, re.DOTALL | re.IGNORECASE)
    if key_vocab_match:
        key_vocab_text = key_vocab_match.group(1).strip()
        if key_vocab_text:
            vocabulary_entries.append(f"=== {filename} - Key Vocabulary ===")
            vocabulary_entries.append(key_vocab_text)
            vocabulary_entries.append("")
    
    # Find Supplementary Vocabulary section
    supp_vocab_match = re.search(supp_vocab_pattern, text, re.DOTALL | re.IGNORECASE)
    if supp_vocab_match:
        supp_vocab_text = supp_vocab_match.group(1).strip()
        if supp_vocab_text:
            vocabulary_entries.append(f"=== {filename} - Supplementary Vocabulary ===")
            vocabulary_entries.append(supp_vocab_text)
            vocabulary_entries.append("")
    
    return vocabulary_entries

def process_pdfs_in_chunks(pdf_directory, output_file, chunk_size=50):
    """Process PDF files in chunks and extract vocabulary."""
    # Get all PDF files
    pdf_files = glob.glob(os.path.join(pdf_directory, "*.pdf"))
    pdf_files.sort()
    
    print(f"Found {len(pdf_files)} PDF files to process")
    
    # Process in chunks
    all_vocabulary = []
    
    for i in range(0, len(pdf_files), chunk_size):
        chunk = pdf_files[i:i+chunk_size]
        print(f"Processing chunk {i//chunk_size + 1}/{(len(pdf_files) + chunk_size - 1)//chunk_size}")
        
        chunk_vocabulary = []
        
        for pdf_path in tqdm(chunk, desc="Processing PDFs"):
            filename = os.path.basename(pdf_path)
            
            # Extract text from PDF
            text = extract_text_from_pdf(pdf_path)
            
            if text:
                # Extract vocabulary sections
                vocab_entries = extract_vocabulary_sections(text, filename)
                chunk_vocabulary.extend(vocab_entries)
        
        # Add chunk results to all vocabulary
        all_vocabulary.extend(chunk_vocabulary)
        
        # Save progress after each chunk
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(all_vocabulary))
        
        print(f"Saved progress to {output_file}")
    
    print(f"Completed processing all {len(pdf_files)} PDF files")
    print(f"Total vocabulary entries extracted: {len([entry for entry in all_vocabulary if entry.startswith('===')])}") 
    
    return all_vocabulary

def main():
    pdf_directory = "7.初中级/english_pod/pdf_files"
    output_file = "7.初中级/english_pod/extracted_vocabulary.txt"
    
    if not os.path.exists(pdf_directory):
        print(f"Directory not found: {pdf_directory}")
        return
    
    print(f"Extracting vocabulary from PDFs in {pdf_directory}")
    
    # Process PDFs and extract vocabulary
    vocabulary = process_pdfs_in_chunks(pdf_directory, output_file)
    
    print(f"Vocabulary extraction completed!")
    print(f"Results saved to: {output_file}")

if __name__ == "__main__":
    main()
